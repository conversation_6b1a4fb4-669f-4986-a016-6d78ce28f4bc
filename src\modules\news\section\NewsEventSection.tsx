import {
  FlatList,
  ListRenderItem,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {EventCardHorizontal} from '../../event/card/EventCards';
import {NewsEvent} from '../../../redux/models/newsEvent';
import {useCallback, useEffect, useState} from 'react';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import {newsEventAction} from '../../../redux/actions/newEventAction';
import {updateArrayWithObjects} from '../../../utils/arrayUtils';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {RootScreen} from '../../../router/router';
import {useNavigation} from '@react-navigation/native';

const NewsEventSection = ({isRefresh}: {isRefresh: boolean}) => {
  const [newsEvents, setNewsEvents] = useState<NewsEvent[]>([]);
  const customerHook = useSelectorCustomerState();
  const navigation = useNavigation<any>();
  // render sự kiện đang diễn ra

  useEffect(() => {
    initData();
  }, [isRefresh]);

  const initData = async () => {
    const customerId = customerHook.data?.Id;
    const response = await newsEventAction.fetch({
      page: 1,
      size: 5,
      sortby: [{prop: 'DateCreated', direction: 'DESC'}],
      customerId,
    });
    if (response.data.length > 0) setNewsEvents(response.data);
  };

  // đăng kí sự kiện thành công
  const onRegisterDone = useCallback((id: string) => {
    setNewsEvents(currentNewsEvents => {
      const foundItem = currentNewsEvents.find(item => String(item.Id) === id);
      if (foundItem) {
        const updatedItem = {...foundItem, isRegistered: true};
        return updateArrayWithObjects(currentNewsEvents, [updatedItem]);
      }
      return currentNewsEvents;
    });
  }, []);

  // Xem thêm
  const onSeeMore = () => {
    navigation.navigate(RootScreen.NewsScreen);
  };

  const renderHorizontalItem: ListRenderItem<NewsEvent> = useCallback(
    ({item}) => (
      <EventCardHorizontal item={item} onRegisterDone={onRegisterDone} />
    ),
    [onRegisterDone],
  );

  return (
    <View>
      <View style={styles.header}>
        <Text style={styles.title}>Tin sự kiện</Text>
        <TouchableOpacity onPress={onSeeMore}>
          <Text style={styles.seeMore}>Xem thêm</Text>
        </TouchableOpacity>
      </View>
      <FlatList
        data={newsEvents}
        renderItem={renderHorizontalItem}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={item => item.Id}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 12,
  },
  title: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  seeMore: {
    ...TypoSkin.body2,
    color: ColorThemes.light.primary_main_color,
  },
  listContent: {
    paddingHorizontal: 16,
  },
});

export default NewsEventSection;
