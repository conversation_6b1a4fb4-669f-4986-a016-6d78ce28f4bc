import {View, ScrollView, RefreshControl} from 'react-native';
import HeaderLogo from '../../Screen/Layout/headers/HeaderLogo';
import CategoryGrid from '../category/CategoryGrid';
import {RootScreen} from '../../router/router';
import {useNavigation} from '@react-navigation/native';
import HotProductsRow from './HotProductsRow';
import HotProductsSection from './HotProductsSection';
import FreeShipProductSection from './section/FreeShipProductSection';
import ProductBestSeller from './productBestSeller';
import SuggestionProductSection from './section/SuggestionProductSection';
import BannerSection from './section/bannerSection';
import MuchSearchSearch from './section/MuchSearchSearch';
import {ColorThemes} from '../../assets/skin/colors';
import {useState} from 'react';

const ProductIndex = () => {
  const navigation = useNavigation<any>();
  const [refreshing, setRefreshing] = useState(false);
  const onSeeMore = (categoryId?: string) => {
    if (categoryId) {
      navigation.navigate(RootScreen.ProductListByCategory, {
        categoryId: categoryId,
      });
    } else {
      navigation.navigate(RootScreen.ProductListByCategory);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };
  return (
    <View
      style={{
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <HeaderLogo />

      <ScrollView
        style={{height: '100%', width: '100%'}}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        {/* banner */}
        <View style={{marginHorizontal: 16}}>
          <BannerSection />
        </View>

        {/* danh mục */}
        <CategoryGrid
          numColumns={3}
          onCategoryPress={category => {
            onSeeMore(category.Id);
          }}
        />

        {/* sản phẩm hot */}
        <HotProductsSection
          pageSize={10}
          onSeeAll={onSeeMore}
          onRefresh={refreshing}
        />

        {/* sản phẩm free ship */}
        <FreeShipProductSection onRefresh={refreshing} />

        {/* sản phẩm được tìm kiếm nhiều */}
        <MuchSearchSearch onSeeMore={onSeeMore} onRefresh={refreshing} />

        {/* banner */}
        <View style={{marginVertical: 12, marginHorizontal: 16}}>
          <BannerSection />
        </View>

        {/* sản phẩm bán chạy */}
        <View style={{marginVertical: 12}}>
          <ProductBestSeller
            isSeeMore
            id="best-selling"
            onPressSeeMore={onSeeMore}
            onRefresh={refreshing}
          />
        </View>

        {/* sản phẩm gợi ý */}
        <SuggestionProductSection
          onSeeAllPress={onSeeMore}
          onRefresh={refreshing}
        />
        <View style={{height: 100}} />
      </ScrollView>
    </View>
  );
};

export default ProductIndex;
