import {useNavigation} from '@react-navigation/native';
import {
  Dispatch,
  PayloadAction,
  UnknownAction,
  createSlice,
} from '@reduxjs/toolkit';
import ProductDA from '../../modules/Product/da';
import {useSelectorShopState} from '../hook/shopHook ';
import {Product} from '../models/product';
import {fetchProducts, updateFavoriteProduct} from '../actions/productAction';
import {updateArrayWithObjects} from '../../utils/arrayUtils';

interface ProductSimpleResponse {
  data: Product[];
  totalCount: number;
  onLoading?: boolean;
  type?: string;
}

const initState: ProductSimpleResponse = {
  data: [],
  totalCount: 0,
  onLoading: false,
};

export const productSlice = createSlice({
  name: 'Product',
  initialState: initState,
  reducers: {
    handleActionsProduct: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case 'GETINFORPRODUCT':
          state.data = action.payload.data;
          break;
        case 'UPDATE':
          state.data = action.payload.data;
          break;
        default:
          break;
      }
      state.onLoading = false;
    },
    setData: <K extends keyof ProductSimpleResponse>(
      state: ProductSimpleResponse,
      action: PayloadAction<{
        stateName: K;
        data: ProductSimpleResponse[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchProducts.pending, state => {
      state.onLoading = true;
    });
    builder.addCase(
      fetchProducts.fulfilled,
      (state: ProductSimpleResponse, action: any) => {
        if (action.payload.isLoadMore) {
          state.data = [...state.data, ...action.payload.data];
        } else {
          state.data = action.payload.data;
        }
        state.totalCount = action.payload.totalCount;
        state.onLoading = false;
      },
    );
    builder.addCase(
      updateFavoriteProduct.fulfilled,
      (state: ProductSimpleResponse, action: any) => {
        state.data = updateArrayWithObjects(state.data, [action.payload]);
        state.onLoading = false;
      },
    );
  },
});

export const {handleActionsProduct} = productSlice.actions;

export default productSlice.reducer;

export class ProductActions {
  static getInforProduct = (shopId: string) => async (dispatch: Dispatch) => {
    const productDA = new ProductDA();
    let data = [
      {
        name: 'Còn hàng',
        number: 0,
        data: [],
      },
      {
        name: 'Hết hàng',
        number: 0,
        data: [],
      },
      {
        name: 'Chờ duyệt',
        number: 0,
        data: [],
      },
      {
        name: 'Vi phạm',
        number: 0,
        data: [],
      },
      {
        name: 'Ẩn',
        number: 0,
        data: [],
      },
    ];
    let ActiveProduct = await productDA.getProducts(shopId, 1);
    if (ActiveProduct.code == 200) {
      data[0].number = ActiveProduct.data.length;
      data[0].data = ActiveProduct.data;
    }
    let OutOfStockProduct = await productDA.getProducts(shopId, 2);
    if (OutOfStockProduct.code == 200) {
      data[1].number = OutOfStockProduct.data.length;
      data[1].data = OutOfStockProduct.data;
    }
    let PendingProduct = await productDA.getProducts(shopId, 3);
    if (PendingProduct.code == 200) {
      data[2].number = PendingProduct.data.length;
      data[2].data = PendingProduct.data;
    }
    let ViolentProduct = await productDA.getProducts(shopId, 4);
    if (ViolentProduct.code == 200) {
      data[3].number = ViolentProduct.data.length;
      data[3].data = ViolentProduct.data;
    }
    let DisableProduct = await productDA.getProducts(shopId, 5);
    if (DisableProduct.code == 200) {
      data[4].number = DisableProduct.data.length;
      data[4].data = DisableProduct.data;
    }

    dispatch(
      handleActionsProduct({
        type: 'GETINFORPRODUCT',
        data: data,
      }),
    );
  };
}
